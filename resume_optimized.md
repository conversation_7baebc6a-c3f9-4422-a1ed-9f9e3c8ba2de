# 潘慧文
**移动开发工程师 | 大前端开发工程师**

📱 15354872767 (微信同号) | ✉️ <EMAIL> | 📍 北京 | 🎂 1992年2月

---

## 💼 工作经验

### 高级移动开发工程师 | 北京牛投邦咨询科技有限公司
**2018年8月 - 2024年4月 (5年8个月)**

#### 核心项目成果
- **多角色权限系统重构** (2020-2024)
  - 主导多家头部基金客户端从单角色升级为多角色多权限架构
  - 设计并实现复杂的角色切换、权限控制、状态管理系统
  - 通过设计模式优化，实现7种不同权限判别动作的统一管理
  - **成果**: 项目如期交付，获得客户高度认可

- **性能优化专项** (2020-2024)
  - 通过代码分包、预加载、启动流程优化将初始化时间**减少60%+**
  - 实施骨架屏、数据缓存、本地化存储等用户体验优化
  - **成果**: 显著提升加载速度和用户体验

#### 技术架构与项目管理
- **移动端**: React Native客户端开发，支持热更新、CI/CD、应用商店送审
- **小程序**: 微信原生、Taro、uni-app多平台开发，模板化架构设计
- **后台管理**: PC端管理系统开发，完整的前后端分离架构
- **项目模板化**: 主导基于WBS的项目模板开发，提升团队开发效率

#### 主要产品线
- 智能营销获客管理系统、栗子理财师、理财顾问云
- 获客排行榜小程序、鹰眼获客能手小程序
- 多家头部基金公司移动端解决方案

### 移动开发工程师 | 北京天云智慧科技有限公司
**2017年6月 - 2018年8月 (1年2个月)**

- 独立开发内部招聘客户端和招聘管理端
- 全栈React Native开发，从0到1完成产品交付

---

## 🛠️ 技术栈

### 前端技术
- **移动端**: React Native (5年+)、微信小程序、Taro、uni-app
- **Web前端**: React、Vue、JavaScript (ES6+)、HTML5、CSS3
- **构建工具**: Webpack、Vite、CI/CD流水线

### 开发工具与流程
- **IDE**: WebStorm、VS Code
- **版本控制**: Git (团队协作、代码审查)
- **项目管理**: 敏捷开发、结果导向的项目交付

### 核心能力
- 数据结构与算法、设计模式应用
- 独立开发能力、AI Agent集成
- 性能优化、架构设计

---

## 🏢 行业经验

**金融科技领域专家** (8年经验)
- 财富管理、证券、保险、基金、理财产品
- 深度理解金融业务流程和合规要求
- 具备完整的金融产品开发生命周期经验

---

## 🎯 核心优势

### 技术领导力
- 主导多个大型项目的技术架构设计和实施
- 具备从0到1的产品开发经验
- 擅长复杂业务场景的技术解决方案设计

### 问题解决能力
- 成功解决多角色权限系统的复杂业务逻辑
- 通过技术手段显著提升产品性能和用户体验
- 具备强大的技术难点攻克能力

### 团队协作
- 优秀的沟通协调能力，跨部门合作经验丰富
- 责任心强，以结果为导向的工作方式
- 有内部技术分享经验，推动团队技术成长

---

## 🎓 教育背景

**本科学士学位** | 软件工程专业  
内蒙古财经大学

---

## 📋 求职信息

- **期望职位**: 移动开发工程师 / 大前端开发工程师
- **工作地点**: 北京
- **到岗时间**: 随时到岗
- **工作年限**: 8年 (2016年6月至今)