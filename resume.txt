
姓名：潘慧文 
性别：男
出生日期：1992-2
求职意向：移动开发工程师/大前端开发工程师
联系方式：15354872767 （同微信）
邮箱：<EMAIL>

2016年6月开始工作到2024年4月。
主要职位为：移动开发工程师。
由近到远依次：
2018年8月到2024年4月：
公司：北京牛投邦咨询科技有限公司。 
工作内容：参与和主导移动端项目的开发。
2018-2020：智能营销获客管理系统（wbs），栗子理财师，理财顾问云，营销助手。获客排行榜（小程序），鹰眼获客能手（小程序）。智能营销管理系统管理后台（PC项目）。
2020-2024：多家国内头部基金客户端，小程序，管理后台。
其中：客户端使用：react native开发，小程序使用：微信原生、taro、uni-app。
主导并参与以wbs为基座的项目模板化开发；项目框架升级；分包，cicd，热更新，送审等经验；小程序模版化，多角色模板切换，云开发，送审等经验；


2017年6月到2018年8月：
公司：北京天云智慧科技有限公司
工作内容：参与和主导移动端项目的开发。
2017-2018:  内部招聘客户端+招聘管理端
其中：全部使用react native开发


技术栈：javascript react react-native vue html css git

熟练：数据结构 设计模式 算法 独立开发 ai agent

行业经验：财富管理 金融 证券 保险 基金 理财

具备：较强的沟通能力和协作能力，责任心强，工作以结果导向。教育背景：
学历：本科、
专业：软件工程、
毕业院校：内蒙古财经大学。

技术能力细节：
熟悉的开发工具
（IDE：WebStorm \ vs code + terminal 、构建工具：webpack \ vite）、版本控制工具（git）、测试经验（如单元测试:无）等。

项目亮点：
具体的技术难点：某基金客户端在逐步升级迭代的过程中，由原来的单角色变成多角色，并且每个角色都对应一套可见范围的权限，每个角色都有可能动态转变为其他角色，不同角色分享的内容到不同的角色时，其所打开详情后的判别动作不同（只能查看不能分享、不能查看、不限制、跳转登录、跳转到身份切换（切换后再跳回内容详情）、跳转到身份审核等待页、跳转到禁用页面）。在由单角色业务升级到多角色多状态多权限的业务背景下，合理使用设计模式和抽象与封装角色身份、状态控制、权限控制、数据同步，本地化存储、数据缓存、预加载等措施，使得项目保质保量如期推进，受到客户好评。
性能优化：在多角色业务升级后，初次启动等待时间较长。通过代码分包，预加载，优化启动流程，骨架屏等层面的优化，使得加载速度和使用体验上得到明显提升。
创新设计或业务效果等可量化成果：如上。

是否参与过开源项目：无
社区分享：有内部技术分享
技术博客：无
技术认证：无

求职区域：北京
到岗时间偏好：随时到岗